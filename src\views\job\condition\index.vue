<template>
  <div class="p-4">
    <div class="container-wrap">
      <div class="table-wrap">
        <BasicTable @register="registerTable" :searchInfo="queryParam">
          <template #toolbar>
            <div class="toolbar">
              <div class="search-wrap">
                <a-button class="addTask" preIcon="ant-design:plus-outlined" @click="addTask('add')"> 新建接警配置 </a-button>
                <a-tree-select
                  v-model:value="selectedDepartment"
                  multiple
                  show-search
                  :treeCheckable="true"
                  :treeCheckStrictly="true"
                  tree-default-expand-all
                  style="min-width: 181px; max-width: auto; margin-right: 8px"
                  :dropdown-style="{ width: 'auto' }"
                  :tree-data="departmentList"
                  :field-names="{ children: 'children', label: 'departName', value: 'orgCode', key: 'orgCode' }"
                  :dropdownMatchSelectWidth="false"
                  placeholder="请选择部门"
                  @change="departmentChange"
                  :maxTagCount="1"
                  :maxTagPlaceholder="(omittedValues) => `+${omittedValues.length} 更多`"
                >
                  <template #title="{ value: val, departName }" style="width: 500px">
                    <div>{{ departName }}</div>
                  </template>
                </a-tree-select>
                <a-select
                  v-if="mychild"
                  ref="select"
                  v-model:value="queryParam.executionDeviceId"
                  style="width: 181px; margin-right: 8px"
                  :options="(mychild as any).actuatingEquipmentOptions"
                  allowClear
                  @change="handleChange"
                  placeholder="全部执行机场"
                ></a-select>
                <a-select
                  v-if="mychild"
                  ref="select"
                  v-model:value="queryParam.triggerDeviceId"
                  style="width: 181px; margin-right: 8px"
                  :options="(mychild as any).triggerEquipmentOptions"
                  allowClear
                  @change="handleChange"
                  placeholder="全部上报设备"
                ></a-select>
                <a-input-search
                  allowClear
                  v-model:value="queryParam.name"
                  placeholder="请输入接警配置名称"
                  style="width: 206px"
                  @search="enterSearch"
                />
              </div>
            </div>
          </template>
          <template #statusFlag="{ record }">
            <div v-if="record.statusFlag">启用</div>
            <div v-else>禁用</div>
          </template>
          <template #triggerDevices="{ record }">
            <span v-for="(item, index) in record.triggerDevices" :key="index" style="margin: 0"
              >{{ index !== 0 ? '，' : '' }}{{ item.deviceName }}</span
            >
          </template>
          <template #triggeringCondition="{ record }">
            <span>{{ record.cnKey }}{{ record.symbol }}{{ record.cnValue }}</span>
          </template>
          <template #action="{ record, column }">
            <TableAction :actions="createActions(record, column)" />
          </template>
          <template #sysOrgCode="{ record }">
            {{ getDepartNameByCode(record.sysOrgCode) }}
          </template>
        </BasicTable>
      </div>
      <!-- 接警配置弹窗 -->
      <AddPoliceInfo
        ref="mychild"
        :visible="PoliceInfoOpen"
        :title="title"
        :policeInfoDialogData="policeInfoDialogData"
        :sysMultiOrgCode="selectedDepartment"
        @handleclose="handleclose"
        @handleSearch="handleSearch"
      ></AddPoliceInfo>
      <AddCondition :pageType="pageType" :conditionData="conditionData" @success="success" @register="registerAddCondition" />
    </div>
  </div>
</template>
<script lang="ts" name="uav-job-condition-index" setup>
  import AddCondition from './add.vue';
  import AddPoliceInfo from './addPoliceInfo.vue';
  import { inject, onMounted, reactive, watch, ref, computed, toRaw } from 'vue';
  import { BasicTable, useTable, TableAction, EditRecordRow, BasicColumn, ActionItem } from '/@/components/Table';
  import { useMessage } from '/@/hooks/web/useMessage';
  import ApiListOptions from '/@/api/type';
  import { ValueTypeOps, DataSourceOps, PageType } from '/@/constants/job';
  import { useModal } from '/@/components/Modal';
  import { throttle } from 'lodash-es';
  import { getDepartNameByCode } from '/@/utils/common/compUtils';

  let myDepartListCodeArr = ref([]);
  const optionsList = ref<any>({
    valueType: ValueTypeOps,
    dataSource: DataSourceOps,
  });
  const mychild = ref<any>(null);
  let queryParam = reactive<any>({
    name: '',
    sysMultiOrgCode: '',
    deptQueryType: 'MULTIPLE',
  });
  let PoliceInfoOpen = ref(false);
  let policeInfoDialogData = reactive<any>({});
  let title = ref<string>('');
  function handleclose() {
    PoliceInfoOpen.value = false;
  }

  function handleChange() {
    searchQuery();
  }

  const selectedDepartment = ref<any[]>([]);
  let lastValidValue = <any>[];

  const departmentList = ref<any[]>([]);

  const columns: BasicColumn[] = [
    {
      title: '接警配置名称',
      dataIndex: 'name',
      key: 'name',
      align: 'center',
    },
    {
      title: '部门',
      dataIndex: 'sysOrgCode',
      key: 'sysOrgCode',
      align: 'center',
      slots: { customRender: 'sysOrgCode' },
    },
    {
      title: '启用状态',
      dataIndex: 'statusFlag',
      key: 'statusFlag',
      align: 'center',
      slots: { customRender: 'statusFlag' },
    },
    {
      title: '触发设备',
      dataIndex: 'triggerDevices',
      key: 'triggerDevices',
      align: 'center',
      slots: { customRender: 'triggerDevices' },
    },
    {
      title: '触发条件',
      dataIndex: 'triggeringCondition',
      key: 'triggeringCondition',
      align: 'center',
      slots: { customRender: 'triggeringCondition' },
    },
    {
      title: '执行设备',
      dataIndex: 'executionDeviceName',
      key: 'executionDeviceName',
      align: 'center',
    },
    {
      title: '最后触发时间',
      dataIndex: 'lastTriggerTime',
      key: 'lastTriggerTime',
      align: 'center',
    },
    // {
    //   title: '数据来源',
    //   dataIndex: 'dataSource',
    //   key: 'dataSource',
    //   align: 'center',
    //   customRender: function ({ text }) {
    //     return optionsList.value.dataSource?.find((item) => item.value === text)?.label || '';
    //   },
    // },
    // {
    //   title: '数据类型',
    //   dataIndex: 'valueType',
    //   key: 'valueType',
    //   align: 'center',
    //   customRender: function ({ text }) {
    //     return optionsList.value.valueType.find((item) => item.value === text)?.label || '';
    //   },
    // },
    // {
    //   title: '取值',
    //   dataIndex: 'valueInfo',
    //   key: 'valueInfo',
    //   align: 'center',
    //   customRender: function ({ text }) {
    //     return text?.length ? text?.toString() : '';
    //   },
    // },
    // {
    //   title: '单位',
    //   dataIndex: 'unit',
    //   key: 'unit',
    //   align: 'center',
    //   customRender: function ({ text }) {
    //     return text || '-';
    //   },
    // },
    // {
    //   title: '创建人',
    //   dataIndex: 'createBy',
    //   key: 'createBy',
    //   align: 'center',
    // },
  ];
  const $http: ApiListOptions | undefined = inject('api');
  const { createMessage, createConfirm } = useMessage();
  //注册table数据
  const [registerTable, { reload, setProps }] = useTable({
    title: '接警配置',
    api: $http?.alarmConfigListPost,
    rowKey: 'id',
    columns: columns,
    // searchInfo: {
    //   ...queryParam,
    // },
    pagination: true,
    clickToRowSelect: false,
    striped: true,
    useSearchForm: false,
    showTableSetting: false,
    bordered: true,
    showIndexColumn: false,
    canResize: false,
    showActionColumn: true,
    actionColumn: {
      width: 200,
      title: '操作',
      fixed: 'right',
      dataIndex: 'action',
      slots: { customRender: 'action' },
    },
  });
  const pageType = ref<string>('');
  const conditionData = ref<any>();
  const [registerAddCondition, { openModal: modalAddCondition }] = useModal();

  const departmentChange = (value) => {
    console.log(value);
    if (value.length === 0) {
      // 禁止清空，恢复上一次值
      selectedDepartment.value = [...lastValidValue];
      return;
    } else {
      lastValidValue = [...value];
    }
    queryParam.sysMultiOrgCode = selectedDepartment.value.map((item) => item.value).join(',');
    searchQuery();
  };

  function normalizeTree(list = []) {
    return list.map((item) => ({
      ...item,
      key: item.orgCode,
      value: item.orgCode,
      disabled: item.disableCheckbox === true,
      children: item.children ? normalizeTree(item.children) : [],
    }));
  }

  onMounted(() => {
    //console.log("初始化")
    const myDepart = localStorage.getItem('myDepartList');
    const myDepartAndChildrenTree = localStorage.getItem('myDepartAndChildrenTree');
    // departmentList.value = myDepartAndChildrenTree ? JSON.parse(myDepartAndChildrenTree) : [];
    departmentList.value = myDepartAndChildrenTree && myDepartAndChildrenTree !== 'null' ? normalizeTree(JSON.parse(myDepartAndChildrenTree)) : [];
    myDepart ? selectedDepartment.value.push(JSON.parse(myDepart)[0].orgCode) : [];
    queryParam.sysMultiOrgCode = JSON.parse(myDepart)[0].orgCode;
    myDepartListCodeArr.value.push(...JSON.parse(myDepart || '[]').map((item) => item.orgCode));
    lastValidValue = [...selectedDepartment.value];
  });

  const searchQuery = throttle(() => {
    setProps({ searchInfo: toRaw(queryParam) });
    reload();
  }, 1000);

  const addTask = (type: string, row?: any) => {
    // pageType.value = PageType.Add;
    // modalAddCondition(true);
    if (type === 'add') {
      title.value = '新建接警配置';
      policeInfoDialogData = {
        statusFlag: false,
      };
    } else {
      console.log('编辑');
      title.value = '编辑接警配置';
      policeInfoDialogData = JSON.parse(JSON.stringify(row));
    }
    PoliceInfoOpen.value = true;
  };

  const isMyPart = (code) => {
    const myDepartList = localStorage.getItem('myDepartList');

    const parsedDepartList = myDepartList ? JSON.parse(myDepartList) : [];
    return parsedDepartList.some((item) => item.orgCode === code);
  };

  const createActions = (record: EditRecordRow, column: BasicColumn): ActionItem[] => {
    return [
      {
        label: '启用',
        onClick: changStatus.bind(null, true, record),
        ifShow: () => {
          return record.status == '0' && isMyPart(record.sysOrgCode);
        },
        // icon: 'ep:edit',
        // onClick: handleAction.bind(null, 'edit', record),
      },
      {
        label: '禁用',
        onClick: changStatus.bind(null, false, record),
        ifShow: () => {
          return record.status == '1' && isMyPart(record.sysOrgCode);
        },
        // icon: 'ep:edit',
        // onClick: handleAction.bind(null, 'edit', record),
      },
      {
        label: '编辑',
        // icon: 'ep:edit',
        onClick: addTask.bind(null, 'edit', record),
        ifShow: () => {
          return isMyPart(record.sysOrgCode)
        },
      },
      {
        label: '删除',
        // icon: 'icon-park-outline:delete',
        onClick: handleAction.bind(null, 'del', record),
        ifShow: () => {
          return isMyPart(record.sysOrgCode)
        },
      },
    ];
  };

  const handleAction = async (type: string, record) => {
    //console.log("功能操作--record", type, record)
    const { id, sysOrgCode } = record;
    switch (type) {
      case 'edit':
        {
          pageType.value = PageType.Edit;
          conditionData.value = { ...record };
          modalAddCondition(true);
        }
        break;
      case 'del':
        {
          createConfirm({
            title: '删除配置',
            content: '确定删除当前配置吗？',
            iconType: 'warning',
            onOk: async () => {
              await $http?.deletealarmConfigDel({ id, sysOrgCode });
              searchQuery();
            },
            onCancel: () => {},
          });
        }
        break;
    }
  };
  const changStatus = async (status: boolean, record) => {
    const { id, sysOrgCode } = record;
    await $http?.alarmConfigUpdateStatusPost({ id, statusFlag: status, sysOrgCode });
    searchQuery();
  };

  const enterSearch = (value) => {
    queryParam.name = value;
    searchQuery();
  };
  const handleSearch = () => {
    searchQuery();
    // visibleDelete.value = false;
  };

  function success() {
    searchQuery();
  }
</script>
<style lang="less" scoped>
  /deep/ .jeecg-basic-table-header__toolbar {
    width: auto;
  }

  /deep/ .cell-content::before {
    content: url('/@/assets/icons/FolderOpenFilled.svg');
    margin-right: 10px;
    vertical-align: middle;
  }

  /deep/ .cell-content {
    cursor: pointer;
  }

  /deep/ .ant-table-thead > tr > th {
    background: #edf6fb;
    color: #0f699a;
  }

  /deep/ .ant-table-tbody > tr.ant-table-row:hover > td,
  .ant-table-tbody > tr > td.ant-table-cell-row-hover {
    background: #f7fbfc;
  }

  /deep/ .ant-popover-buttons {
    display: flex;
  }
  /deep/ .ant-table-empty .ant-table-tbody > tr.ant-table-placeholder {
    height: 600px;
  }
  /deep/ .ant-table-body {
    height: 600px;
  }
  .container-wrap {
    position: relative;
    display: flex;
    .table-wrap {
      width: 100%;
      .customCol {
        .openDir:hover {
          cursor: pointer;
        }
      }
      .toolbar {
        display: flex;
        margin-top: 3px;
        display: flex;
        .search-wrap {
          .addTask {
            background: linear-gradient(315deg, #2997d2 0%, #1880b9 100%);
            box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.16);
            border-radius: 2px;
            color: white;
            margin-right: 8px;
          }
        }
      }
    }
  }
</style>
<style lang="less" scoped>
  .container-wrap {
    .ant-picker-body {
      background: rgba(237, 248, 255, 0.5);
      border-radius: 4px;
    }
  }
</style>
