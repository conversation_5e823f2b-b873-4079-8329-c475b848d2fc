<template>
  <div class="Container Row" id="detail-container">
    <a-modal
      v-model:visible="showLogin"
      :keyboard="false"
      :maskClosable="false"
      :closable="false"
      title="输入提取码"
      width="100%"
      wrap-class-name="full-modal"
      @ok="handleShareExtractCode"
      :getContainer="getContainer"
      :cancelButtonProps="{ style: { display: 'none' } }"
    >
      <a-input v-model:value="passWord" placeholder="请输入提取码" @pressEnter="handleShareExtractCode"></a-input>
    </a-modal>
    <contrastModal ref="contrastModalRef" :data="hoverMod"></contrastModal>
    <div class="example" v-show="loadMask">
      <a-spin size="large" tip="Loading..." />
    </div>
    <div class="pictureBox">
      <div class="left">
        <div class="previewContainer">
          <!--普通图片-->
          <template v-if="hoverMod.fileType === '1' || hoverMod.fileType === '20'">
            <div class="img-container">
              <AiLbel
                :imgUrl="hoverMod.hdUrl"
                ref="AiLbelRef"
                :AILabelValue="AILabelValue"
                :LabelInfo="hoverMod.labelInfo"
                :pageFrom="pageFrom"
                :isShowMenu="showByMyPart"
                @positionsLabels="positionsLabels"
                @notarize="notarize"
                @loadMaskFun="loadMaskFun"
                @delLabel="delLabel"
              ></AiLbel>

              <div class="userMapBox" :style="generateStyles()" v-if="pageFrom !== 'share'">
                {{ userMapObj[userMap] }}
              </div>
              <!-- @mousewheel.stop="onMouseWheel"
              @mousedown="onMouseDown"
              @mousemove="onMouseMove"
              @mouseleave="onMouseLeave"
              @mouseup="onMouseUp" -->
              <!-- <canvas id="canvas" width="800" height="800"></canvas> -->
              <!-- <img :src="hoverMod.hdUrl" alt="无资源" :style="{ transform: imageTransform }" ref="imageRef" /> -->
            </div>
            <div class="infoBox">
              <div class="text fileName">
                <Tooltip :title="hoverMod.fileName" placement="top">
                  <div class="name"> {{ hoverMod.fileName }} </div>
                </Tooltip>
              </div>
              <div class="tiem">{{
                `${dayjs(hoverMod.createTime).format('YYYY-MM-DD HH:mm:ss')}
                ${formatSize(hoverMod.fileSize)} ${hoverMod.dimension || ''}`
              }}</div>
            </div>
            <div class="operatingBox">
              <div
                class="operatingBot"
                v-if="NoContrast && hasPermission('uav:file:location:file:count') && pageFrom !== 'share'"
                @click="operation('contrast')"
              >
                <div class="iconBox">
                  <img :src="jobImgUrl('img-compare')" />
                </div>
                <div class="text">对比</div>
              </div>
              <div class="operatingBot" @click="operation('prev')">
                <div class="iconBox">
                  <LeftCircleOutlined class="icon" />
                </div>
                <div class="text">前一张</div>
              </div>
              <div class="operatingBot" @click="operation('next')">
                <div class="iconBox">
                  <RightCircleOutlined class="icon" />
                </div>
                <div class="text">后一张</div>
              </div>
              <div class="operatingBot" @click="operation('down')">
                <div class="iconBox">
                  <DownloadOutlined class="icon" />
                </div>
                <div class="text">下载</div>
              </div>
              <div class="operatingBot" @click="operation('del')" v-if="pageFrom !== 'share' && showByMyPart">
                <div class="iconBox">
                  <DeleteOutlined class="icon" />
                </div>
                <div class="text">删除</div>
              </div>
            </div>
          </template>
          <!--视频-->

          <template v-if="hoverMod.fileType === '2'">
            <div class="videoPlayContainer">
              <videoPlay width="100%" height="100%" title="预览视频" :src="hoverMod.url" :poster="hoverMod.hdUrl" />
            </div>
            <div class="infoBox">
              <div class="text fileName">
                <Tooltip :title="hoverMod.fileName" placement="top">
                  <div class="name"> {{ hoverMod.fileName }} </div>
                </Tooltip>
              </div>
              <div class="tiem">{{
                `${dayjs(hoverMod.createTime).format('YYYY-MM-DD HH:mm:ss')}
                ${formatSize(hoverMod.fileSize)} ${hoverMod.dimension || ''}`
              }}</div>
            </div>
            <div class="operatingBox">
              <div class="operatingBot" @click="operation('prev')">
                <div class="iconBox">
                  <LeftCircleOutlined class="icon" />
                </div>
                <div class="text">前一张</div>
              </div>
              <div class="operatingBot" @click="operation('next')">
                <div class="iconBox">
                  <RightCircleOutlined class="icon" />
                </div>
                <div class="text">后一张</div>
              </div>
              <div class="operatingBot" @click="operation('down')">
                <div class="iconBox">
                  <DownloadOutlined class="icon" />
                </div>
                <div class="text">下载</div>
              </div>
              <div class="operatingBot" @click="operation('del')" v-if="pageFrom !== 'share'">
                <div class="iconBox">
                  <DeleteOutlined class="icon" />
                </div>
                <div class="text">删除</div>
              </div>
            </div>
          </template>
          <!--全景图-->

          <template v-if="hoverMod.fileType === '3'">
            <VuePannellum :src="hoverMod.url"></VuePannellum>
            <div class="infoBox">
              <div class="text fileName">
                <Tooltip :title="hoverMod.fileName" placement="top">
                  <div class="name"> {{ hoverMod.fileName }} </div>
                </Tooltip>
              </div>
              <div class="tiem">{{ hoverMod.createTime }}</div>
            </div>
            <div class="operatingBox">
              <div class="operatingBot" @click="operation('prev')">
                <div class="iconBox">
                  <LeftCircleOutlined class="icon" />
                </div>
                <div class="text">前一张</div>
              </div>
              <div class="operatingBot" @click="operation('next')">
                <div class="iconBox">
                  <RightCircleOutlined class="icon" />
                </div>
                <div class="text">后一张</div>
              </div>
              <div class="operatingBot" @click="operation('down')">
                <div class="iconBox">
                  <DownloadOutlined class="icon" />
                </div>
                <div class="text">下载</div>
              </div>
              <div class="operatingBot" @click="operation('del')" v-if="pageFrom !== 'share'">
                <div class="iconBox">
                  <DeleteOutlined class="icon" />
                </div>
                <div class="text">删除</div>
              </div>
            </div>
          </template>
          <!--正射影像-->

          <template v-if="hoverMod.fileType === '4'">
            <!-- <VuePannellum :src="hoverMod.hdUrl"></VuePannellum> -->
            <!-- <OrthophotoViewer :url="hoverMod.url"></OrthophotoViewer> -->

            <map3d :center="map3dConfig.scene.center" style="height: calc(100vh - 96px)" :layers="map3dConfig.layers"></map3d>

            <div class="infoBox">
              <div class="text fileName">
                <Tooltip :title="hoverMod.fileName" placement="top">
                  <div class="name"> {{ hoverMod.fileName }} </div>
                </Tooltip>
              </div>
              <div class="tiem">{{ hoverMod.createTime }}</div>
            </div>
            <div class="operatingBox" v-show="NoContrast && pageFrom !== 'share'">
              <div class="operatingBot" @click="operation('contrast')">
                <div class="iconBox">
                  <img :src="jobImgUrl('img-compare')" />
                </div>
                <div class="text">对比</div>
              </div>
              <!-- <div class="operatingBot" @click="operation('prev')">
                <div class="iconBox">
                  <LeftCircleOutlined class="icon" />
                </div>
                <div class="text">前一张</div>
              </div>
              <div class="operatingBot" @click="operation('next')">
                <div class="iconBox">
                  <RightCircleOutlined class="icon" />
                </div>
                <div class="text">后一张</div>
              </div>
              <div class="operatingBot" @click="operation('down')">
                <div class="iconBox">
                  <DownloadOutlined class="icon" />
                </div>
                <div class="text">下载</div>
              </div>
              <div class="operatingBot" @click="operation('del')">
                <div class="iconBox">
                  <DeleteOutlined class="icon" />
                </div>
                <div class="text">删除</div>
              </div> -->
            </div>
          </template>
          <!--3D模型-->

          <template v-if="hoverMod.fileType === '5'">
            <div>
              <achieveDisplay :options="hoverMod" :shareInfo="{ fsrId: fsrId1, projectId: projectId1, token: shareToken }"></achieveDisplay>
            </div>

            <div class="infoBox">
              <div class="text fileName">
                <Tooltip :title="hoverMod.fileName" placement="top">
                  <div class="name"> {{ hoverMod.fileName }} </div>
                </Tooltip>
              </div>
              <div class="tiem">{{ hoverMod.createTime }}</div>
            </div>
            <!-- <div class="operatingBox">
              <div class="operatingBot" @click="operation('prev')">
                <div class="iconBox">
                  <LeftCircleOutlined class="icon" />
                </div>
                <div class="text">前一张</div>
              </div>
              <div class="operatingBot" @click="operation('next')">
                <div class="iconBox">
                  <RightCircleOutlined class="icon" />
                </div>
                <div class="text">后一张</div>
              </div>
              <div class="operatingBot" @click="operation('down')">
                <div class="iconBox">
                  <DownloadOutlined class="icon" />
                </div>
                <div class="text">下载</div>
              </div>
              <div class="operatingBot" @click="operation('del')">
                <div class="iconBox">
                  <DeleteOutlined class="icon" />
                </div>
                <div class="text">删除</div>
              </div>
            </div> -->
          </template>

          <!--点云模型-->
          <template v-if="hoverMod.fileType === '7'">
            <div>
              <modeling :options="hoverMod"></modeling>
            </div>

            <div class="infoBox">
              <div class="text fileName">
                <Tooltip :title="hoverMod.fileName" placement="top">
                  <div class="name"> {{ hoverMod.fileName }} </div>
                </Tooltip>
              </div>
              <div class="tiem">{{ hoverMod.createTime }}</div>
            </div>
            <!-- <div class="operatingBox">
              <div class="operatingBot" @click="operation('prev')">
                <div class="iconBox">
                  <LeftCircleOutlined class="icon" />
                </div>
                <div class="text">前一张</div>
              </div>
              <div class="operatingBot" @click="operation('next')">
                <div class="iconBox">
                  <RightCircleOutlined class="icon" />
                </div>
                <div class="text">后一张</div>
              </div>
              <div class="operatingBot" @click="operation('down')">
                <div class="iconBox">
                  <DownloadOutlined class="icon" />
                </div>
                <div class="text">下载</div>
              </div>
              <div class="operatingBot" @click="operation('del')">
                <div class="iconBox">
                  <DeleteOutlined class="icon" />
                </div>
                <div class="text">删除</div>
              </div>
            </div> -->
          </template>
        </div>
        <div class="carousel" v-show="hoverMod.fileType != 5 && hoverMod.fileType != 7 && hoverMod.fileType != 4">
          <a-carousel arrows :dots="false" ref="carouselRef" :infinite="false">
            <template #prevArrow>
              <div class="custom-slick-arrow" style="left: 10px; z-index: 1" @click.prevent="prevPage">
                <left-circle-outlined />
              </div>
            </template>

            <template #nextArrow>
              <div class="custom-slick-arrow" style="right: 10px" @click.prevent="nextPage">
                <right-circle-outlined />
              </div>
            </template>
            <div class="thumbnail-container" v-for="item in slidesToShow" :key="item">
              <div
                class="thumbnail"
                :class="{ hover: (item - 1) * visiblePageSize + indexs === hoverIndex }"
                v-for="(items, indexs) in slidesList(item - 1)"
                @click.prevent="preview(item - 1, indexs)"
                :key="indexs"
              >
                <img :src="items.thumbnailUrl" alt="无图" />
              </div>
            </div>
          </a-carousel>
        </div>
      </div>
      <div class="right">
        <div class="share-list" v-if="pageFrom === 'share'">
          <div class="headTitle">
            <div class="title">文件</div>
          </div>
          <div class="file-list-box">
            <div v-for="(item, index) in fileList" :key="item.fileId" :class="['file-item', docType === '1' ? 'model-item' : '']">
              <span @click="updateCurImage(index)">{{ item.fileName }}</span>
              <a-button
                v-if="docType === '1'"
                type="primary"
                shape="round"
                size="small"
                preIcon="ant-design:download-outlined"
                @click="downloadShareFile(item.fileDirectoryShareZipInfo.fileId)"
                >下载</a-button
              >
            </div>
          </div>
          <a-button
            class="download-all"
            v-if="docType === '2' && fileList.length"
            type="primary"
            shape="round"
            size="large"
            preIcon="ant-design:download-outlined"
            @click="downloadShareFile(fileList[0].fileDirectoryShareZipInfo.fileId)"
            >下载</a-button
          >
        </div>
        <div class="result-info" v-else style="position: relative; display: flex; flex-direction: column; width: 100%; height: 100%">
          <div class="headTitle">
            <div class="title">详细信息</div>
            <div class="close">
              <CloseOutlined @click="closeBot" style="color: white; font-size: 18px; cursor: pointer" />
            </div>
          </div>
          <div class="headInfoBox">
            <div class="infoBox">
              <div class="infoList">
                <div class="title">文件名称</div>
                <div class="content row">
                  <template v-if="editStatus">
                    <a-input
                      ref="inputRef"
                      v-model:value="editFileName"
                      placeholder="请输入新的文件名称"
                      style="color: #fff; background: #091822; border: 1px solid #3a464e"
                      @pressEnter="(e) => handleEnter('rename', e.target.value)"
                    />
                    <CloseOutlined @click="edit('name', false)" style="color: #94989b; margin-left: 10px; font-size: 18px; cursor: pointer" />
                    <check-outlined
                      @click="handleEnter('rename', editFileName)"
                      style="color: #94989b; margin-left: 10px; font-size: 18px; cursor: pointer"
                    />
                  </template>

                  <template v-else>
                    <div class="text fileName">
                      <Tooltip :title="hoverMod.fileName" placement="top">
                        <div class="name"> {{ hoverMod.fileName }} </div>
                      </Tooltip>
                    </div>
                    <EditOutlined
                      v-if="hoverMod.fileName && showByMyPart"
                      @click="edit('name')"
                      style="color: #94989b; font-size: 18px; cursor: pointer"
                    />
                  </template>
                </div>
              </div>
              <div class="infoList">
                <div class="title">文件类型</div>
                <div class="text">{{ getFileType(hoverMod.fileType) }}</div>
              </div>
              <div class="infoList">
                <div class="title">任务名称</div>
                <div class="text">{{ hoverMod.jobName }}</div>
              </div>
              <div class="infoList">
                <div class="title">航线名称</div>
                <div class="text">{{ hoverMod.flightName }}</div>
              </div>
              <div class="infoList">
                <div class="title">分辨率</div>
                <div class="text">{{ hoverMod.dimension }}</div>
              </div>
              <div class="infoList">
                <div class="title">文件大小</div>
                <div class="text">{{ formatSize(hoverMod.fileSize) }}</div>
              </div>
              <div class="infoList">
                <div class="title">拍摄负载</div>
                <div class="text">{{ hoverMod.payload }}</div>
              </div>
              <div class="infoList">
                <div class="title">拍摄时间</div>
                <div class="text">{{ dayjs(hoverMod.createTime).format('YYYY-MM-DD HH:mm:ss') }} </div>
              </div>
              <!-- <div class="infoList">
              <div class="title">标签</div>
              <div class="tag-content">
                <div class="labelTag" v-for="(item, index) in hoverMod.tags" :key="index">
                  {{ item.tag }}
                  <CloseCircleOutlined class="labelTagIcon" @click="delTag(item.id)" />
                </div>

                <template v-if="editTagStatus">
                  <a-select
                    v-model:value="editTagName"
                    show-search
                    allowClear
                    placeholder="请输入标签"
                    style="width: 120px; height: 32px; margin-right: 10px; color: #fff"
                    :default-active-first-option="false"
                    :show-arrow="false"
                    :filter-option="false"
                    :not-found-content="null"
                    :options="searchList"
                    @change="handleChange"
                    @search="handleSearch"
                  ></a-select>
                  <a-button type="primary" size="small" @click="handleSubmit">确定</a-button>
                  <CloseOutlined @click="edit('addTag', false)" style="color: #94989b; margin-left: 10px; font-size: 18px; cursor: pointer" />
                </template>
                <div class="addTag" @click="edit('addTag')" v-if="!editTagStatus">
                  <PlusOutlined class="icon" />
                  <div class="addText">标签</div>
                </div>
              </div>
            </div> -->
              <div v-if="showByMyPart && (hoverMod.fileType === '1' || hoverMod.fileType === '20')" class="infoList">
                <div class="title">标签</div>
                <div class="tree-select">
                  <!-- <a-tree-select
                  v-model:value="AILabelValue"
                  :labelInValue="true"
                  :field-names="{ children: 'children', value: 'id', label: 'name' }"
                  show-search
                  style="width: 100%"
                  :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
                  placeholder="请选择标签"
                  allow-clear
                  tree-default-expand-all
                  :tree-data="treeData"
                  tree-node-filter-prop="name"
                >
                </a-tree-select> -->

                  <TreeSelect
                    v-model:value="AILabelValue"
                    :treeData="treeData"
                    :fieldNames="{ children: 'children', key: 'id', label: 'name' }"
                  ></TreeSelect>
                </div>
              </div>
              <!-- 成因分析 -->
              <div v-if="hoverMod.fileType === '1'" class="infoList">
                <div class="title">成因分析</div>
                <div class="describe">
                  <a-textarea v-model:value="causeAnalysis" placeholder="请选择标签" :showCount="false" :rows="4" style="color: #7b8388" disabled />
                </div>
              </div>
              <!-- 养护/维修建议 -->
              <div v-if="hoverMod.fileType === '1'" class="infoList">
                <div class="title">养护/维修建议</div>
                <div class="describe">
                  <a-textarea v-model:value="recommendations" placeholder="请选择标签" :showCount="false" :rows="4" style="color: #7b8388" disabled />
                </div>
              </div>
              <div v-if="hoverMod.fileType === '1' || hoverMod.fileType === '20'" class="infoList">
                <div class="title">问题描述</div>
                <div class="describe">
                  <a-textarea
                    v-model:value="description"
                    placeholder="请输入问题描述"
                    :showCount="true"
                    :maxlength="100"
                    :rows="4"
                    :disabled="!showByMyPart"
                  />
                </div>
              </div>
              <div class="infoList">
                <div class="title">位置</div>
                <div class="map-wrap">
                  <div class="text">
                    <!-- <AimOutlined /> -->
                    <div class="place"> <EnvironmentTwoTone style="margin-right: 8px" /> 当前拍摄位置 </div>
                    <!-- <div class="coordinatesBox">
                  {{ currentPosition[0] || 0 + '°E' }}<br />{{ currentPosition[1] || 0 + '°N' }}
                </div> -->
                    <!-- <span>
                  当前拍摄位置 <br /> 经度:{{ hoverMod?.shootPosition?.lng }}<br />纬度:{{ hoverMod?.shootPosition?.lat }}
                </span> -->
                  </div>
                </div>
              </div>
              <div class="infoList">
                <div class="title">经度</div>
                <div class="location">
                  <div class="coordinatesBox"> {{ currentPosition[0] || 0 }}°E </div>
                </div>
              </div>

              <div class="infoList">
                <div class="title">纬度</div>
                <div class="location">
                  <div class="coordinatesBox"> {{ currentPosition[1] || 0 }}°N </div>
                  <div class="map-content">
                    <!-- 地图容器 -->
                    <div id="pic-container"></div>
                  </div>
                </div>
              </div>
              <div class="infoList">
                <div class="title">地图显示</div>
                <div class="text">
                  <a-switch v-model:checked="showMap" @change="switchMap" :disabled="!showByMyPart" />
                </div>
              </div>
              <!-- <div class="infoList" v-if="hoverMod.fileType === '1'">
              <a-button @click="labelSaveButton">确认标注</a-button>
            </div> -->
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" name="dataManage-dataManage-detail" setup>
import { inject, onMounted, onUnmounted, computed, ref, reactive, watch, defineProps, defineEmits, nextTick, toRaw } from 'vue';
import dayjs from 'dayjs';
import * as mars3d from 'mars3d';
import { message } from 'ant-design-vue';
import defaultMapConfig from '../../mapView/map3d.config.json';
import { useBasemapConfig } from '@/hooks/web/useBasemapConfig';
import {
  getfileDetail,
  postFileDetailList,
  putRenameFile,
  postFileTagAdd,
  deleteFileTagDel,
  postSearchTag,
  postDownloadFile,
  delSingleFile,
  getDirDictionary,
  getLayerInfo,
  shareGetLayerInfo,
  markCategoryList,
  markImagesMerge,
  labelGet,
  labelSave,
  delFileAllLabel,
  shareLogin,
  shareList,
  shareDownload,
} from './data.api';
import { useGo } from '/@/hooks/web/usePage';
import { useRouter, useRoute } from 'vue-router';
import { useMultipleTabStore } from '/@/store/modules/multipleTab';
// import AMapLoader from '@amap/amap-jsapi-loader';
import { useMessage } from '/@/hooks/web/useMessage';
import {
  LeftCircleOutlined,
  RightCircleOutlined,
  DownloadOutlined,
  DeleteOutlined,
  PlusOutlined,
  CloseOutlined,
  EditOutlined,
  AimOutlined,
  CloseCircleOutlined,
  EnvironmentTwoTone,
  CheckOutlined,
} from '@ant-design/icons-vue';
import { videoPlay } from '/@/components/Video/index';
import { Tooltip } from 'ant-design-vue';
import { VuePannellum } from '/@/components/VuePannellum';
import { downloadFile } from '/@/api/common/api';
import OrthophotoViewer from './components/OrthophotoViewer.vue';
import map3d from './components/map3d.vue';
import AiLbel from './components/ailabel/index.vue';
import { getIconUrl } from '/@/utils';
import achieveDisplay from '/@/views/achieveDisplay/index.vue';
import modeling from '@/views/dataManage/dataManage/components/modeling/index.vue';
import coordtransform from 'coordtransform';
import TreeSelect from './components/TreeSelect/index.vue';
import contrastModal from './components/contrastModal/index.vue';
import { imageComparison } from './components/contrastModal/data.api';
import { usePermission } from '/@/hooks/web/usePermission';
const { hasPermission } = usePermission();

const jobImgUrl = (name: string, suffix = 'svg') => {
  return getIconUrl(name, 'icons/', suffix);
};
const jobImgUrls = (name: string, suffix = 'svg') => {
  return getIconUrl(name, 'icons/remoteOverTwo/', suffix);
};
const getContainer = () => {
  return document.getElementById('detail-container');
};
const map3dConfig = ref({
  scene: { center: { lat: 22.697928708158926, lng: 114.11831786715567, alt: 341.1514795250993, heading: 0, pitch: -90 } },
  layers: [],
});
const { configMap } = useBasemapConfig();

const defaultImageOps = {
  isDragging: false,
  scale: 1, // 图片缩放
  imageLeft: 0,
  imageTop: 0,
  dragStartX: 0,
  dragStartY: 0,
  startImageX: 0,
  startImageY: 0,
};

const $http: ApiListOptions | undefined = inject('api');
const go = useGo();
const curRoute = useRoute();
const { fullPath } = curRoute;
const router = useRouter();
const tabStore = useMultipleTabStore();
const fileTypeList = ref<any[]>([]); // 文件类型的字典数据
const { createMessage, createConfirm } = useMessage();
let map = ref(); // 高德地图实例对象
let AmapObj = reactive<any>({}); // 高德地图实例对象
const emit = defineEmits();
// const imageTransform = ref<string>(''); // 图片的transform 参数
const imageRef = ref(null);
const imageOps = reactive({ ...defaultImageOps });
const hoverMod = ref<any>({}); // 激活模块
const hoverIndex = ref<number>(0); // 激活模块的索引
const pageNo = ref<number>(1); // 页码(接口返回)
const pageSize = ref<number>(10); // 页数(接口返回)
const pages = ref<number>(0); // 总页数(接口返回)
const visiblePageNo = ref<number>(1); //轮播展示的页码
const visiblePageSize = 5; // 轮播展示的分页条数
const carouselRef = ref();
const fileList = ref<any[]>([]);
// 修改文件名称
const editStatus = ref<boolean>(false); // 文件名称是否处于修改状态
const editFileName = ref<string>(''); // 临时文件名称
const inputRef = ref(null);
// 新增tag
const editTagStatus = ref<boolean>(false); // tag是否处于新增状态
const editTagName = ref<string>(''); // tag(编辑时添加的)
const inputTagRef = ref(null);
// 远程数据+模糊搜索
const searchList = ref<any[]>([]);
const searchValue = ref();
let timeout: any = null;
const currentValue = ref();

let AiLbelRef = ref();
let currentPosition: any = ref([]); //当前经纬度 用于转换经纬度

let description = ref('');
let AILabelValue = ref();
let aiLabelList = ref([]);
let defaultLabelList = ref([]);
let contrastModalRef = ref();
let userMap = ref();
let NoContrast = ref();
let loadMask = ref(false);
const userMapObj = {
  1: '已标注',
  10: '待确认',
  false: '未标注',
};
//存储接收成因分析，养护/维修建议，用于回显数据
let causeAnalysisArr = reactive<any[]>([]);
let recommendationsArr = reactive<any[]>([]);
//用来拿后台数据保存、判断
let tagData = reactive<any[]>([]);
let causeAnalysis = ref('');
let recommendations = ref('');
const pageFrom = ref();
// 是否需要输入密码
const showLogin = ref(false);
const passWord = ref('');

//加密的fsrId和projectId
const fsrId = ref('');
const projectId = ref('');

//解密的fsrId和projectId
const fsrId1 = ref('');
const projectId1 = ref('');
const shareToken = ref('');

const docType = ref('');
const isEms = localStorage.getItem('environmentTag') === 'privateCloud' ? true : false;
const showByMyPart = ref(true);
const downloadShareFile = async (fileId) => {
  const res = await shareDownload({
    fileId,
    fsrId: fsrId1.value,
    projectId: projectId1.value,
    token: shareToken.value,
  });
  const { data = {} } = res;
  if (data?.code === 200 && data?.result) {
    window.open(data?.result + '?response-content-type=application/octet-stream');
  } else {
    createMessage.warning(data.message);
  }
};
const handleShareExtractCode = async () => {
  //登录
  const res = await shareLogin({
    fsrId: fsrId.value,
    projectId: projectId.value,
    passWord: passWord.value,
  });
  const { data = {} } = res;
  if (data.code === 200) {
    showLogin.value = false;
    //取接口返回的最新的字段
    shareToken.value = data.result.token;
    fsrId1.value = data.result.fsrId;
    projectId1.value = data.result.projectId;

    initData();
  } else {
    createMessage.error(data.message);
  }
};
// 成因分析回显方法
const changeCauseAnalysis = () => {
  const arr1: any = [];
  causeAnalysisArr.forEach((item) => {
    item.causeDescription = item.causeDescription ? item.causeDescription : '暂无成因分析';
    arr1.push(`${item.name}：\n${item.causeDescription}`);
  });
  causeAnalysis.value = arr1.join('\n');
};
// 养护/维修建议回显方法
const changeRecommendations = () => {
  const arr2: any = [];
  recommendationsArr.forEach((item) => {
    item.adviseDescription = item.adviseDescription ? item.adviseDescription : '暂无养护/维修建议';
    arr2.push(`${item.name}：\n${item.adviseDescription}`);
  });
  recommendations.value = arr2.join('\n');
};

// 去重标签方法
const deWeightTag = (tempArr) => {
  let result: any = [];
  let obj = {};
  for (let i = 0; i < tempArr?.length; i++) {
    if (!obj[tempArr[i].labels.labelId]) {
      result.push(tempArr[i]);
      obj[tempArr[i].labels.labelId] = true;
    }
  }
  return result;
};

const tagsData = (tagData) => {
  console.log('去重', tagData);

  // 去重标签
  const deWeightTagData = deWeightTag(tagData);
  deWeightTagData.forEach((item) => {
    const obj = {
      name: item.labels.name,
      labelId: item.labels.labelId,
      causeDescription: item.labels.causeDescription,
      adviseDescription: item.labels.adviseDescription,
    };
    causeAnalysisArr.push(obj);
    recommendationsArr.push(obj);
  });
  //
  changeCauseAnalysis();
  //
  changeRecommendations();
};
watch(
  () => AILabelValue.value,
  (value) => {
    console.log(value);
    // console.log('看是否有这个标签的图片',aiLabelList.value);
    // 判断是否画图，没有画则删除上一个，防止点一个页面就加一个
    const aiLabeindex = aiLabelList.value.findIndex((val: any) => val.labels.name === causeAnalysisArr[causeAnalysisArr.length - 1].name);
    if (aiLabeindex < 0) {
      causeAnalysisArr.pop();
      recommendationsArr.pop();
    }

    const obj = {
      name: AILabelValue.value.name,
      labelId: AILabelValue.value.value,
      causeDescription: AILabelValue.value.causeDescription,
      adviseDescription: AILabelValue.value.adviseDescription,
    };
    const idx = causeAnalysisArr.findIndex((item) => item.labelId === AILabelValue.value.value);
    if (idx < 0) {
      causeAnalysisArr.push(obj);
      recommendationsArr.push(obj);
    }
    changeCauseAnalysis();
    changeRecommendations();
  }
);

watch(
  () => hoverMod.value,
  (value) => {
    console.log(value, 'hoverMod.value');
    const params = {
      fileId: value?.fileId,
      position: {
        lat: value?.shootPosition?.lat,
        lng: value?.shootPosition?.lng,
      },
      sysOrgCode: curRoute.query.sysOrgCode,
    };
    if (hasPermission('uav:file:location:file:count')) {
      imageComparison.queryLocationFileCount(params).then((res) => {
        if (res > 1) {
          NoContrast.value = true;
        } else {
          NoContrast.value = false;
        }
        console.log(res, 'queryLocationFileCount');
      });
    } else {
      NoContrast.value = false;
    }

    if (value.fileLabelStatus === '1') {
      userMap.value = 1;
    } else if (value.fileLabelStatus === '10') {
      userMap.value = 10;
    } else {
      userMap.value = false;
    }
    // if (hoverMod.value.fileType != '1' && hoverMod.value.fileType != '20' ) return;
    description.value = value?.labelInfo?.description || '';

    if (!hoverMod.value.labelInfo || hoverMod.value.labelInfo?.defaultLabelList.length < 1) {
      AiLbelRef.value && AiLbelRef.value.RemoveAllTags();
    }

    // AiLbelRef.value && AiLbelRef.value.RemoveAllTags();
    // labelGet(value.fileId)
    //   .then((res) => {
    //     // console.log(res);
    //     defaultLabelList.value = res.defaultLabelList;
    //     userMap.value = true;
    //   })
    //   .catch(() => {
    //     userMap.value = false;
    //     if (AiLbelRef.value) {
    //       AiLbelRef.value.RemoveAllTags();
    //     }
    //   });
  }
);

const generateStyles = () => {
  if (userMap.value == 1) {
    const sytle = {
      width: '72px',
      height: '32px',
      background: 'rgba(23,115,68,0.6)',
      borderRadius: '2px',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
    };
    return sytle;
  } else if (userMap.value == 10) {
    const sytle = {
      width: '72px',
      height: '32px',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      background: 'rgba(255,134,49,0.3)',
      border: '1px solid rgba(255,134,49,0.5)',
      borderRadius: '2px',
      color: '#FF8631',
    };
    return sytle;
  } else {
    const sytle = {
      width: '72px',
      height: '32px',
      background: 'rgba(185,35,35,0.6)',
      borderRadius: '2px',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
    };
    return sytle;
  }
  // return {
  //   backgroundColor: userMap.value ? '#14b669' : 'red',
  // };
};
const download = (downfile) => {
  if (localStorage.getItem('debug') != 'debug') {
    return;
  }
  const tmpLink = document.createElement('a');
  const objectUrl = URL.createObjectURL(downfile);

  tmpLink.href = objectUrl;
  tmpLink.download = downfile.name;
  document.body.appendChild(tmpLink);
  tmpLink.click();

  document.body.removeChild(tmpLink);
  URL.revokeObjectURL(objectUrl);
};
const labelSaveButton = () => {
  const data = {
    defaultLabelList: aiLabelList.value || [],
    fileId: hoverMod.value.fileId || '',
    description: description.value,
    labelType: 0,
    sysOrgCode: curRoute.query.sysOrgCode,
  };
  labelSave(data).then((res) => {
    if (res.data.code == 200) {
      createMessage.success('标注成功!');
      userMap.value = 1;
      loadMask.value = false;

      const imgFile = AiLbelRef.value.GetBackImage('featureLayer');
      download(imgFile);
      const markFile = AiLbelRef.value.GetOverLayImage('featureLayer');
      download(markFile);
      const labelFile = AiLbelRef.value.GetOverLayImage('label-id');
      download(labelFile);

      const formData = new FormData();
      formData.append('fileId', data.fileId);
      formData.append('files', imgFile);
      formData.append('files', markFile);
      formData.append('files', labelFile);
      markImagesMerge(formData);
    }
  });
};

let treeData = ref([]);

const positionsLabels = (event) => {
  aiLabelList.value = removeDuplicates(event);
  console.log(aiLabelList.value, 'event-positionsLabels');
};

const notarize = () => {
  labelSaveButton();
  setTimeout(() => {
    // updateFileListDetail(pageNo.value, 'curPage');
    updateCurFileDetail();
  }, 1500);
};

const delLabel = () => {
  delFileAllLabel(hoverMod.value.fileId).then((res) => {
    createMessage.success('删除成功!');
    setTimeout(() => {
      // updateFileListDetail(pageNo.value, 'curPage');
      updateCurFileDetail();
    }, 300);
  });
};

const loadMaskFun = () => {
  loadMask.value = true;
};

const removeDuplicates = (arr) => {
  if (!Array.isArray(arr)) {
    return;
  }
  const idSet = new Set();
  return arr.filter((item) => {
    if (!idSet.has(item.id)) {
      idSet.add(item.id);
      return true;
    }
    return false;
  });
};

// 录播的分片数量
const slidesToShow = computed(() => {
  return fileList.value.length ? Math.ceil(fileList.value.length / visiblePageSize) : 1;
});

const slidesList = computed(() => (index) => {
  return fileList.value.length ? fileList.value.slice(index * visiblePageSize, (index + 1) * visiblePageSize) : [];
});

const getFileType = computed(() => (fileTypeId) => fileTypeList.value.find((item) => item.value === fileTypeId)?.label || '');

const imageTransform = computed(() => `translate(${imageOps.imageLeft}px, ${imageOps.imageTop}px) scale(${imageOps.scale})`);

onMounted(async () => {
  const { pf, t, f, p, dt, isMyPart } = curRoute.query;
  showByMyPart.value = isMyPart === 'true' ? true : false;
  await initMap();

  //分享页不需要密码时，也需要调用登录接口
  if (pf === 'share') {
    // t：提取码类型（0：免密分享，1：系统随机生成，2：自定义）；aes加密参数(f：分享记录id，p：项目id)
    pageFrom.value = pf;
    showLogin.value = t === '0' ? false : true;
    fsrId.value = f;
    projectId.value = p;
    docType.value = dt;

    //免密时，直接调用提取码接口，获取token等信息
    //非免密时，输入密码后，手动调用登录接口获取token等信息
    t === '0' && (await handleShareExtractCode());
  } else {
    // 分享页不需要一进入就请求数据
    await initData();
  }

  window.addEventListener('keyup', handleKeyUp);
});
onUnmounted(() => {
  window.removeEventListener('keyup', handleKeyUp);
});
const handleKeyUp = (event) => {
  // 检查当前聚焦的元素是否是输入框、下拉框等表单元素
  const activeElement = document.activeElement;
  const isFormElement = ['INPUT', 'TEXTAREA', 'SELECT'].includes(activeElement?.tagName || '');

  if (isFormElement) return;

  const keyCode = event.keyCode || event.which;
  if (keyCode == 39) {
    operation('next');
  }
  if (keyCode == 37) {
    operation('prev');
  }
};

const initData = async () => {
  let res;
  if (pageFrom.value === 'share') {
    res = await shareList({
      fsrId: fsrId1.value,
      projectId: projectId1.value,
      token: shareToken.value,
      pageNo: 1,
      pageSize: 9999,
    });
  } else {
    console.log('curRoute.query', curRoute.query);
    const result = await getDirDictionary({ sysOrgCode: curRoute.query.sysOrgCode });
    if (Object.keys(result)?.length) {
      Object.keys(result).forEach((key) => {
        if (['fileType'].includes(key)) {
          fileTypeList.value = result[key];
        }
      });
    }
    res = await postFileDetailList({
      fileId: curRoute.query.fileId,
      dirId: curRoute.query.dirId,
      dirPageNo: curRoute.query.dirPage,
      dirPageSize: curRoute.query.dirPageSize,
      fileType: curRoute.query.fileType,
      device: curRoute.query.device,
      createTime: curRoute.query.createTime,
      fileName: curRoute.query.fileName,
      parentId: curRoute.query.parentId,
      pageNo: curRoute.query.pageNo,
      pageSize: curRoute.query.pageSize,
      isLabel: curRoute.query.isLabel,
      labelIds: curRoute.query.labelIds,
      beginTime: curRoute.query.beginTime,
      endTime: curRoute.query.endTime,
      column: curRoute.query.column,
      order: curRoute.query.order,
      sysOrgCode: curRoute.query.sysOrgCode,
    });
  }

  const { data = {} } = res;
  //console.log("data", data)
  if (data?.code === 200 && data?.result) {
    pageNo.value = data?.result?.current;
    pages.value = data?.result?.pages;
    pageSize.value = data?.result?.size;
    fileList.value = data?.result?.records || [];
    const fileListTemp = fileList.value;
    if (fileListTemp?.length) {
      const hoverIndex = fileListTemp.findIndex((item) => item.fileId === curRoute.query.fileId);
      // console.log('hoverIndex--------', hoverIndex);
      if (hoverIndex !== -1) {
        if (hoverIndex >= visiblePageSize) {
          visiblePageNo.value = Math.ceil((hoverIndex + 1) / visiblePageSize);
          //console.log("初始化--visiblePageNo.value", visiblePageNo.value)
          carouselRef.value.goTo(visiblePageNo.value - 1);
        } else {
          visiblePageNo.value = 1;
        }
        updateCurImage(hoverIndex);
      } else {
        visiblePageNo.value = 1;
        updateCurImage(0);
      }
    }
  }

  // 分享页去掉需要系统校验的接口调用，否则会跳转到系统登录页
  pageFrom.value !== 'share' &&
    markCategoryList({ markFrom: 'user', sysOrgCode: curRoute.query.sysOrgCode }).then((res) => {
      treeData.value = res.map((item) => {
        return { ...item, disabled: true };
      });
    });
};
let mapView: any = null;
let mapbacklayer: any = null;
let currentMarker: any = null;
const initMap = async () => {
  // try {
  //   const AMap = await AMapLoader.load({
  //     key: '59c61f064c552f2ace25ff0c1d6465a7', // 申请好的Web端开发者Key，首次调用 load 时必填
  //     version: '2.0', // 指定要加载的 JS API 的版本，缺省时默认为 1.4.15
  //     plugins: [], // 需要使用的的插件列表，如比例尺'AMap.Scale'等
  //   });
  //   AmapObj = AMap;
  //   //console.log("加载完地图---------------")
  //   map.value = new AMap.Map('pic-container', {
  //     // center: [114.118725, 22.697947],
  //     zoom: 16,
  //     viewMode: '3D', //使用3D视图
  //   });
  // } catch (e) {
  //   console.log('地图初始化', e);
  // }
  try {
    if (mapView) {
      mapView.destroy();
      mapView = null;
    }
    await nextTick(); // 确保DOM更新完成

    const container = document.getElementById('pic-container');
    if (!container) {
      throw new Error('Map pic-container not found');
    }
    const mars3dConfig = mars3d.Util.merge(defaultMapConfig.map3d, toRaw(configMap));
    mapView = new mars3d.Map('pic-container', mars3dConfig);
    mapbacklayer = new mars3d.layer.GraphicLayer({
      allowDrillPick: true, // 如果存在坐标完全相同的图标点，可以打开该属性，click事件通过graphics判断
    });
    mapView.addLayer(mapbacklayer);
    let latitudeLongitude = localStorage.getItem('latitudeLongitude'),
      center = latitudeLongitude ? JSON.parse(latitudeLongitude) : [116.478935, 39.997761];
    mapView.setCameraView({ lng: center[0], lat: center[1], alt: 300 }, { duration: 0.1 });
  } catch (error) {
    console.error('Map initialization failed:', error);
    showMap.value = false;
    message.error('地图初始化失败');
  }
};

var centerMarker = null;

//更新地图
const handleChangeMap = (lng, lat, thumbnail) => {
  // console.log('1111111',thumbnail);

  console.log('更新地图==map', lng, lat);
  if (lng && lat) {
    currentPosition.value = isEms ? [lng, lat] : coordtransform.wgs84togcj02(lng, lat);
    if (currentMarker) {
      mapView.graphicLayer.removeGraphic(currentMarker); // 移除之前的标点
    }

    // 添加标点
    currentMarker = new mars3d.graphic.BillboardEntity({
      position: [lng, lat, 0], // 位置使用经纬度
      flyTo: true, // 飞向该点

      style: {
        image: jobImgUrls('点', 'png'),
        clampToGround: true,
      },
    });

    mapView.graphicLayer.addGraphic(currentMarker);
    // currentPosition.value = coordtransform.wgs84togcj02(lng, lat);
    // let transition = coordtransform.wgs84togcj02(lng, lat);
    // const position = new AmapObj.LngLat(transition[0], transition[1]);
    // map.value.setZoomAndCenter(16, position); // 传入经纬度，设置地图中心点

    // centerMarker && map.value.remove(centerMarker);
    // centerMarker = new AMap.Marker({
    //   position: position,
    //   offset: new AMap.Pixel(0, 0),
    //   icon: new AMap.Icon({ image: thumbnail, size: new AMap.Size(30, 30), imageSize: new AMap.Size(30, 30) }),
    // });
    // map.value.add(centerMarker);
  } else {
    // if (typeof map.value !== 'undefined') {
    //   map.value.clearMap();
    // }
    if (mapView && currentMarker) {
      mapView.graphicLayer.removeGraphic(currentMarker); // 移除之前的标点
    }
    currentPosition.value = [0, 0];
  }
};

// 格式化文件大小
const formatSize = (size) => {
  // const isM = size >= (1024 * 1024)
  // const isKB = size >= 1024
  // const text = size ? isM ? (size  / (1024 * 1024)).toFixed(2) : isKB ? (size  / (1024 )).toFixed(2) : Number(size || 0) ? size.toFixed(2) : 0 : 0
  // return `${text}${isM ? 'M' : isKB ? 'KB' : 'B'}`

  if (size >= 1024 * 1024 * 1024) {
    const text = (size / (1024 * 1024 * 1024)).toFixed(2);
    return `${text}${'GB'}`;
  } else if (size >= 1024 * 1024) {
    const text = (size / (1024 * 1024)).toFixed(2);
    return `${text}${'M'}`;
  } else if (size >= 1024) {
    const text = (size / 1024).toFixed(2);
    return `${text}${'KB'}`;
  }
  return `${size}${'B'}`;

  // case size >= (1024 * 1024):
  // const text2 = (size  / (1024 * 1024)).toFixed(2)
  // return `${text2}${'M'}`;
  // case size >= (1024 ):
  // const text3 = (size  / (1024 )).toFixed(2)
  // return `${text3}${'KB'}`;
  // default :
  // return `${size}${'B'}`;  // 如果没有与表达式相同的值，则执行该代码
};

// 图片的缩放和拖动
function onMouseWheel(event) {
  event.preventDefault();
  const wheelDelta = event.deltaY;
  if (wheelDelta > 0 && imageOps.scale > 0.4) {
    imageOps.scale -= 0.1;
  } else if (wheelDelta < 0) {
    imageOps.scale += 0.1;
  }
}
function onMouseDown(event) {
  imageOps.isDragging = true;
  imageOps.dragStartX = event.clientX;
  imageOps.dragStartY = event.clientY;
  imageOps.startImageX = imageOps.imageLeft;
  imageOps.startImageY = imageOps.imageTop;
}
function onMouseMove(event) {
  event.preventDefault();
  if (imageOps.isDragging) {
    const offsetX = event.clientX - imageOps.dragStartX;
    const offsetY = event.clientY - imageOps.dragStartY;
    imageOps.imageLeft = imageOps.startImageX + offsetX;
    imageOps.imageTop = imageOps.startImageY + offsetY;
  }
}
function onMouseUp() {
  imageOps.isDragging = false;
}
function onMouseLeave() {
  imageOps.isDragging = false;
}

// 重置缩放参数 / 退出编辑状态
const resetImageOpsAndEdit = () => {
  Object.keys(defaultImageOps).forEach((key) => (imageOps[key] = defaultImageOps[key]));
  editTagStatus.value = false;
  editTagName.value = '';
  editStatus.value = false;
  editFileName.value = '';
};

// 更新文件的列表查询
const updateFileListDetail = async (newPageNo: number, pageType: string) => {
  const res = await postFileDetailList({
    // fileId: curRoute.query.fileId,
    dirId: curRoute.query.dirId,
    dirPageNo: curRoute.query.dirPage,
    dirPageSize: curRoute.query.dirPageSize,
    fileType: curRoute.query.fileType,
    device: curRoute.query.device,
    createTime: curRoute.query.createTime,
    fileName: curRoute.query.fileName,
    parentId: curRoute.query.parentId,
    pageNo: newPageNo,
    pageSize: curRoute.query.pageSize,
    isLabel: curRoute.query.isLabel,
    labelIds: curRoute.query.labelIds,
    beginTime: curRoute.query.beginTime,
    endTime: curRoute.query.endTime,
    column: curRoute.query.column,
    order: curRoute.query.order,
    sysOrgCode: curRoute.query.sysOrgCode,
  });
  const { data = {} } = res;
  //console.log("data", data)
  if (data?.code === 200) {
    if (data?.result && data?.result?.records?.length) {
      const pageNoTemp = pageNo.value;
      pageNo.value = newPageNo;
      fileList.value = data?.result?.records || [];
      const fileListTemp = fileList.value;
      if (fileListTemp?.length) {
        if (pageType === 'prevPage') {
          // 操作的是'上一页'则激活最后一条数据
          const hoverIndexTemp = fileListTemp?.length - 1;
          if (hoverIndexTemp >= visiblePageSize) {
            visiblePageNo.value = Math.ceil((hoverIndexTemp + 1) / visiblePageSize);
            carouselRef.value.goTo(visiblePageNo.value - 1);
          }
          updateCurImage(hoverIndexTemp);
        } else if (pageType === 'nextPage') {
          // 操作的是'下一页'则激活第一条数据
          visiblePageNo.value = 1;
          carouselRef.value.goTo(0);
          updateCurImage(0);
        } else if (pageType === 'curPage') {
          // 更新当前页则激活索引不变
          let hoverIndexTemp = hoverIndex.value;
          if (hoverIndexTemp > fileListTemp?.length - 1 || pageNoTemp !== pageNo.value) hoverIndexTemp = fileListTemp?.length - 1;
          if (hoverIndexTemp >= visiblePageSize) {
            visiblePageNo.value = Math.ceil((hoverIndexTemp + 1) / visiblePageSize);
            carouselRef.value.goTo(visiblePageNo.value - 1);
          }
          //console.log("删除数据--hoverIndexTemp", hoverIndexTemp)
          updateCurImage(hoverIndexTemp);
        }
      }
    } else {
      if (newPageNo - 1 >= 1) {
        updateFileListDetail(newPageNo - 1, pageType); // 当前页没有数据则递归取上一页数据
      }
    }
  }
};

// 更新当前操作的文件信息(单个)
const updateCurFileDetail = async () => {
  if (!hoverMod.value.fileId) return;
  const res = await getfileDetail({ id: hoverMod.value.fileId });
  const { data = {} } = res;
  if (data?.code === 200 && data?.result) {
    const fileListTemp = fileList.value;
    const index = fileListTemp.findIndex((item) => item.fileId === hoverMod.value.fileId);
    fileListTemp[index] = data?.result;
    hoverMod.value = fileListTemp[index];
    fileList.value = fileListTemp;
    // 先重置
    causeAnalysisArr = [];
    recommendationsArr = [];
    tagData = data.result.labelInfo?.defaultLabelList || [];
    tagsData(tagData);
  } else {
    createMessage.warning(data.message);
  }
};

// 更新激活的图片
const updateCurImage = async (curHoverIndex) => {
  const fileListTemp = fileList.value;
  hoverIndex.value = curHoverIndex;
  hoverMod.value = fileListTemp?.length ? (fileListTemp[curHoverIndex] ? fileListTemp[curHoverIndex] : fileListTemp[0]) : [];
  if (!fileListTemp[curHoverIndex]) {
    carouselRef.value.goTo(0);
  }

  if (hoverMod.value.fileType === '4') {
    let res = {};
    if (pageFrom.value === 'share') {
      res = await shareGetLayerInfo({ fileId: hoverMod.value.fileId, fsrId: fsrId1.value, projectId: projectId1.value, token: shareToken.value }); // '17096965912355577'
    } else {
      res = await getLayerInfo({ fileId: hoverMod.value.fileId }); // '17096965912355577'
    }

    //res.url = res.url.substr(5)
    //res.url = '//data.mars3d.cn/file/img/zkd-dq.png'

    map3dConfig.value.scene.center.lat = res.center.lat;
    map3dConfig.value.scene.center.lng = res.center.lng;
    map3dConfig.value.scene.center.alt = res.center.alt;
    map3dConfig.value.layers.length = 0;
    map3dConfig.value.layers.push({ url: res.url, layerName: res.layerName });
  }

  resetImageOpsAndEdit();
  //更新地图
  handleChangeMap(hoverMod.value?.shootPosition?.lng, hoverMod.value?.shootPosition?.lat, hoverMod.thumbnailUrl);
  //拿切换图片后的标签数据
  tagData = fileListTemp[curHoverIndex].labelInfo?.defaultLabelList || [];
  tagsData(tagData);

  if (AILabelValue.value) {
    // 当切换图片时，判断当前选中的标签是否已经存在
    const obj = {
      name: AILabelValue.value.name,
      labelId: AILabelValue.value.value,
      causeDescription: AILabelValue.value.causeDescription,
      adviseDescription: AILabelValue.value.adviseDescription,
    };
    const idx = causeAnalysisArr.findIndex((item) => item.labelId === AILabelValue.value.value);

    if (idx < 0) {
      causeAnalysisArr.push(obj);
      recommendationsArr.push(obj);
    }
    //
    changeCauseAnalysis();
    //
    changeRecommendations();
  }
};

const operation = async (type: string) => {
  switch (type) {
    case 'prev': // 前一张
      {
        causeAnalysisArr = [];
        recommendationsArr = [];
        const hoverIndexTemp = hoverIndex.value;
        const pageNoTemp = visiblePageNo.value;
        if (hoverIndexTemp === 0) {
          if (pageNo.value <= 1) {
            createMessage.warning('已是第一张');
            return;
          }
          updateFileListDetail(pageNo.value - 1, 'prevPage');
          return;
        }
        if (hoverIndexTemp - 1 < (pageNoTemp - 1) * visiblePageSize) {
          AiLbelRef.value && AiLbelRef.value.RemoveAllTags();
          //console.log("滑块的上一页")
          carouselRef.value.prev();
          visiblePageNo.value = pageNoTemp - 1;
          const curHoverIndex = (pageNoTemp - 1) * visiblePageSize - 1;
          updateCurImage(curHoverIndex);
          return;
        }
        updateCurImage(hoverIndexTemp - 1);
        // AiLbelRef.value && AiLbelRef.value.RemoveAllTags();
        // AiLbelRef.value && AiLbelRef.value.isHide = false;
      }
      break;
    case 'next': // 后一张
      {
        causeAnalysisArr = [];
        recommendationsArr = [];
        const hoverIndexTemp = hoverIndex.value;
        const pageNoTemp = visiblePageNo.value;
        if (hoverIndexTemp === fileList.value?.length - 1) {
          if (pageNo.value === pages.value) {
            createMessage.warning('已是最后一张');
            return;
          }
          // 更新列表
          updateFileListDetail(pageNo.value + 1, 'nextPage');
          return;
        }
        if (hoverIndexTemp + 1 >= pageNoTemp * visiblePageSize) {
          AiLbelRef.value && AiLbelRef.value.RemoveAllTags();
          //console.log("滑块的下一页")
          carouselRef.value.next();
          visiblePageNo.value = pageNoTemp + 1;
          const curHoverIndex = pageNoTemp * visiblePageSize;
          updateCurImage(curHoverIndex);
          return;
        }
        updateCurImage(hoverIndexTemp + 1);
        // AiLbelRef.value && AiLbelRef.value.RemoveAllTags();
        // AiLbelRef.value && AiLbelRef.value.isHide = false;
        console.log(AiLbelRef.value, 'AiLbelRef.value');
      }
      break;
    case 'down': // 下载
      let res;
      if (pageFrom.value === 'share') {
        res = await shareDownload({
          fileId: hoverMod.value.fileId,
          fsrId: fsrId1.value,
          projectId: projectId1.value,
          token: shareToken.value,
        });
      } else {
        res = await postDownloadFile({ id: hoverMod.value.fileId });
      }

      const { data = {} } = res;
      if (data?.code === 200 && data?.result) {
        // 跳过axios请求url的过程，直接打开文件url。将响应头设置为application/octet-stream，意思是媒体类型用于下载，而不是显示。不加响应头，直接window.open遇到图片活视频时会打开预览或者播放，而不是下载。
        window.open(data?.result + '?response-content-type=application/octet-stream');

        // 通过axios请求后端返回的文件的url,将文件数据下载到浏览器内存中，待数据下载完成后，浏览器才开始执行下载动作。
        // 弊端：对于大文件的下载，用户点击下载后，需要等待一段时间等到数据下载到浏览器内存中，才能看到浏览器的下载器进行下载。
        // downloadFile(data?.result, hoverMod.value.fileName, {}, true)
      } else {
        createMessage.warning(data.message);
      }
      break;
    case 'del': // 删除
      {
        createConfirm({
          title: '删除文件',
          content: '确定要删除文件吗？',
          iconType: 'warning',
          onOk: async () => {
            const res = await delSingleFile({ id: hoverMod.value.fileId });
            //console.log("删除文件==res", res)
            const { data = {} } = res;
            if (data.code === 200) {
              // 删除成功后拉取接口更新数据
              updateFileListDetail(pageNo.value, 'curPage');
            } else {
              createMessage.warning(data.message);
            }
          },
          onCancel: () => {},
        });
      }
      break;
    case 'contrast':
      contrastModalRef.value.open();
      break;
  }
};

const preview = (index, indexs) => {
  // 切换后重置成因分析，养护维修建议
  causeAnalysisArr = [];
  recommendationsArr = [];
  // AiLbelRef.value && AiLbelRef.value.RemoveAllTags();
  //console.log("index, indexs", index, indexs)
  const curHoverIndex = index * visiblePageSize + indexs;
  updateCurImage(curHoverIndex);
};

// 上一页
const prevPage = () => {
  const fileListTemp = fileList.value;
  const pageNoTemp = visiblePageNo.value;
  if (fileListTemp?.length) {
    if (pageNoTemp === 1) {
      if (pageNo.value <= 1) {
        createMessage.warning('已是第一页');
        return;
      }
      updateFileListDetail(pageNo.value - 1, 'prevPage');
      return;
    }
    visiblePageNo.value = pageNoTemp - 1;
  }
};

// 下一页
const nextPage = () => {
  const fileListTemp = fileList.value;
  const pageNoTemp = visiblePageNo.value;
  if (fileListTemp?.length) {
    //console.log("下一页", pageNoTemp + 1, Math.ceil(fileListTemp?.length / visiblePageSize))
    if (pageNoTemp + 1 > Math.ceil(fileListTemp?.length / visiblePageSize)) {
      if (pageNo.value === pages.value) {
        createMessage.warning('已是最后一页');
        return;
      }
      updateFileListDetail(pageNo.value + 1, 'nextPage');
      return;
    }
    visiblePageNo.value = pageNoTemp + 1;
  }
};

/**
 * 编辑文件信息
 * @param {string} type 激活的字段(类型)
 * @param {boolean} isEdit 激活/退出编辑状态
 *
 */
const edit = (type: string, isEdit: boolean = true) => {
  switch (type) {
    case 'name': // 编辑文件名称
      if (isEdit) {
        editStatus.value = true;
        editFileName.value = hoverMod.value.fileName.substring(0, hoverMod.value.fileName.lastIndexOf('.'));
        inputRef.value && inputRef.value.focus();
      } else {
        editStatus.value = false;
        editFileName.value = '';
      }
      break;
    case 'addTag': // 新增tag
      if (isEdit) {
        editTagStatus.value = true;
        searchValue.value = '';
        inputTagRef.value && inputTagRef.value.focus();
      } else {
        editTagStatus.value = false;
        editTagName.value = '';
      }
      break;
  }
};

const handleEnter = async (type: string, value: any) => {
  if (!value) return;
  switch (type) {
    case 'rename': // 变更文件名称
      {
        // console.log("变更文件名称--e", value)
        const res = await putRenameFile({
          id: hoverMod.value.fileId,
          newName: `${value}.${hoverMod.value.fileName.substring(hoverMod.value.fileName.lastIndexOf('.') + 1)}`,
          oldName: hoverMod.value.fileName,
        });
        const { data = {} } = res;
        if (data.code === 200) {
          editStatus.value = false;
          editFileName.value = '';
          updateCurFileDetail();
        } else {
          createMessage.warning(data.message);
        }
      }
      break;
    case 'addTag': //新增标签
      {
        const res = await postFileTagAdd({ fileId: hoverMod.value.fileId, tag: value });
        const { data = {} } = res;
        if (data.code === 200) {
          editTagStatus.value = false;
          editTagName.value = '';
          searchValue.value = '';
          searchList.value = [];
          updateCurFileDetail();
        } else {
          createMessage.warning(data.message);
        }
      }
      break;
  }
};

// 删除标签
const delTag = async (tagId) => {
  const res = await deleteFileTagDel(tagId);
  const { data = {} } = res;
  if (data.code === 200) {
    editTagStatus.value = false;
    editTagName.value = '';
    updateCurFileDetail();
  } else {
    createMessage.warning(data.message);
  }
};

const searchData = (value: string, callback: any) => {
  if (timeout) {
    clearTimeout(timeout);
    timeout = null;
  }

  currentValue.value = value;

  const fetch = async () => {
    const res = await postSearchTag({ tag: value });
    const { data = {} } = res;
    if (data.code === 200) {
      if (currentValue.value === value) {
        const result = data.result || [];
        const resData: any[] = result.map((item, index) => {
          return {
            value: index + 1, // 后端给的id都为null,前端自定义下唯一标识
            label: item.tag,
          };
        });
        callback(resData);
      }
    }
  };
  timeout = setTimeout(fetch, 300);
};

const handleSearch = (val: any) => {
  //console.log("handleSearch--data", val)
  if (val) {
    searchValue.value = val;
    searchData(val, (d: any[]) => (searchList.value = d));
  }
};

const handleChange = (val: any) => {
  //console.log("handleChange--data", val)
  searchValue.value = val;
};

const handleSubmit = () => {
  //console.log("handleSubmit--data", searchValue.value, searchList.value)
  let tagText = '';
  const item = searchList.value.find((item) => item.value == searchValue.value)?.label;
  if (item) {
    tagText = item;
  } else if (searchValue.value) {
    tagText = searchValue.value;
  }
  //console.log("新增标签--", tagText)
  if (tagText) {
    handleEnter('addTag', tagText);
  }
};

function closeBot() {
  resetData();
  console.log('closeBot', fullPath, router);
  tabStore.closeTabByKey(fullPath, router, false);

  // 回退到列表页
  go('/dataManage/dataManage');
  // router.back();
}

const switchMap = async (checked: boolean) => {
  console.log('checked', checked);
  const result = await $http.putManageShowMap({ fileId: hoverMod.value.fileId, showMap: Number(checked) });
  if (!result) {
    hoverMod.value.showMap = !checked;
  }
};
const showMap = computed({
  get() {
    return Boolean(hoverMod.value.showMap);
  },
  set(value) {
    hoverMod.value.showMap = Number(value);
  },
});

const resetData = () => {
  hoverMod.value = {};
  hoverIndex.value = 0;
  pageNo.value = 1;
  pageSize.value = 10;
  visiblePageNo.value = 1;
  fileList.value = [];
  editStatus.value = false;
  editFileName.value = '';
  editTagStatus.value = false;
  editTagName.value = '';
  searchValue.value = '';
  searchList.value = [];
  resetImageOpsAndEdit();
};
</script>

<style lang="less" scoped>
/deep/ .scrollbar__wrap {
  overflow: inherit;
}

/* For carousel */
:deep(.slick-slide) {
  text-align: center;
  height: 140px;
  line-height: 140px;
  background: #000;
  overflow: hidden;
}

:deep .ant-select-clear {
  color: #fff;
  background: #000;
}

:deep(.slick-arrow.custom-slick-arrow) {
  width: 25px;
  height: 25px;
  font-size: 25px;
  color: #fff;
  background-color: rgba(31, 45, 61, 0.11);
  transition: ease all 0.3s;
  // opacity: 0.3;
  z-index: 1;
}

:deep(.slick-arrow.custom-slick-arrow:before) {
  display: none;
}

:deep(.slick-arrow.custom-slick-arrow:hover) {
  color: #fff;
  opacity: 0.5;
}

:deep(.slick-slide h3) {
  color: #fff;
}

:deep(.full-modal) {
  /* 覆盖模态框容器 */
  .ant-modal {
    position: fixed;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    max-width: 100vw !important;
    width: 100vw !important;
    height: 100vh !important;
    margin: 0 !important;
    padding: 0 !important;
  }

  /* 内容区域撑满 */
  .ant-modal-content {
    height: 100vh !important;
    display: flex;
    flex-direction: column;
    border-radius: 0 !important;
  }

  /* Body 区域弹性填充 */
  .ant-modal-body {
    flex: 1;
    overflow: auto;
    display: flex;
    align-items: center;
    justify-content: center;
    input {
      width: 200px;
      height: 40px;
    }
    .ant-input {
      border: 2px solid #d9d9d9;
      border-radius: 4px;
    }
  }
}
.Container {
  width: 100%;
  height: 100%;
  position: relative;

  .example {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(38, 51, 59, 0.5);
    z-index: 99999;
  }
}

.Container > * {
  min-width: 30px;
  position: absolute;
  top: 0;
  height: 100%;
}

.Row {
  display: flex;
  flex-direction: row;
}

.pictureBox {
  width: 100%;
  height: 100%;
  padding: 15px 15px 0px;
  display: flex;

  .left {
    width: 79%;
    height: 100%;
    background-color: #091822;
    .previewContainer {
      position: relative;
      height: calc(100% - 140px);

      .img-container {
        width: 100%;
        height: 100%;
        overflow: hidden;
        display: flex;
        justify-content: center;

        > img {
          width: auto;
          height: 100%;
        }

        .userMapBox {
          position: absolute;
          top: 2.3%;
          right: 5%;
          color: #fff;
          padding: 0 5px;
          border-radius: 3px;
          z-index: 99;
        }
      }

      .videoPlayContainer {
        width: 100%;
        height: 100%;
      }

      .infoBox {
        width: 410px;
        height: 81px;
        background: linear-gradient(90deg, rgba(0, 0, 0, 0.3) 0%, rgba(0, 0, 0, 0) 100%);
        backdrop-filter: blur(2px);
        padding: 15px 18px;
        position: absolute;
        top: 24px;
        left: 32px;
        z-index: 999;

        .text {
          font-size: 20px;
          font-family: PingFangSC-Medium, PingFang SC;
          font-weight: 500;
          color: #ffffff;
          line-height: 28px;
          width: 390px;

          &.fileName {
            flex-wrap: nowrap;

            .name {
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            }
          }
        }

        .tiem {
          font-size: 14px;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #ffffff;
          opacity: 0.7;
          line-height: 20px;
        }
      }

      .operatingBox {
        width: 373px;
        height: 44px;
        background: rgba(21, 21, 21, 0.4);
        border-radius: 4px;
        backdrop-filter: blur(5px);
        position: absolute;
        left: 50%;
        bottom: 60px;
        transform: translate(-50%, -50%);
        display: flex;
        justify-content: space-around;
        align-items: center;
        padding: 0 25px;
        z-index: 999;

        .operatingBot {
          display: flex;
          align-items: center;
          cursor: pointer;

          .iconBox {
            display: flex;
            align-items: center;
            margin-right: 7px;

            .icon {
              font-size: 18px;
              color: #ffffff;
            }
          }

          .text {
            font-size: 14px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #ffffff;
          }
        }
      }
    }

    .carousel {
      height: 140px;

      /deep/.thumbnail-container {
        width: 100%;
        height: 140px;
        display: flex !important;
        align-items: center;
        padding: 0 60px;
        justify-content: center;

        .thumbnail {
          width: 170px !important;
          height: 90px;
          margin-right: 25px;

          img {
            width: 100%;
            height: 100%;
          }

          &.hover {
            img {
              width: 100%;
              height: 100%;
              border-radius: 4px;
              border: 4px solid #1a9efd;
            }
          }
        }
      }
    }
  }

  .right {
    width: 21%;
    height: 100%;
    background-color: #091822;
    display: flex;
    flex-direction: column;
    overflow: auto;
    .file-list-box {
      max-height: 350px;
      overflow-y: scroll;
      padding: 0 10px;
      .file-item {
        color: #fff;
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 10px;
        &.model-item {
          span {
            display: inline-block; /* 或者使用 block，根据你的布局需求 */
            width: 200px; /* 设置固定宽度 */
            white-space: nowrap; /* 防止文本换行 */
            overflow: hidden; /* 隐藏超出部分 */
            text-overflow: ellipsis; /* 超出部分显示省略号 */
          }
        }
      }
    }
    .download-all {
      margin: 0 auto;
      display: block;
    }
    .headTitle {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-top: 12px;
      padding: 12px 14px 14px 22px;

      .title {
        font-size: 16px;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: #ffffff;
      }

      .close {
        width: 40px;
        height: 40px;
        background-color: #3c454b;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        // opacity: 0.39;
      }
    }

    .headInfoBox {
      width: 100%;
      height: calc(100% - 78px);
      position: absolute;
      flex: 1;
      overflow: scroll;
      top: 78px;
      .infoBox {
        padding: 0 22px;
        width: 100%;
        height: 100%;
        .infoList {
          display: flex;
          // align-items: center;
          margin-bottom: 16px;
          .describe {
            width: 230px;
            position: relative;
            :deep(.ant-input) {
              background: rgba(216, 216, 216, 0.02);
              border-radius: 2px;
              border: 1px solid rgba(255, 255, 255, 0.2);
              font-size: 14px;
              color: #ffffff;
            }
            :deep(.ant-input-textarea-show-coun::after) {
              top: 1px;
              position: absolute;
              color: #ffffff;
            }
            .ant-input-textarea-show-count::after {
              position: absolute;
              color: #ffffff;
              right: 14px;
              bottom: 1px;
            }
          }

          .location {
            .coordinatesBox {
              background: rgba(216, 216, 216, 0.05);
              border-radius: 2px;
              height: 30px;
              color: #838a8f;
              display: flex;
              align-items: center;
              width: 225px;
              padding-left: 10px;
            }

            .map-content {
              width: 220px;
              height: 200px;
              margin-top: 15px;

              #pic-container {
                width: 220px;
                height: 200px;
              }
            }
          }

          .title {
            width: 70px;
            font-size: 14px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #7b8388;
            margin-right: 19px;
          }

          .text {
            font-size: 14px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            width: 226px;
            color: #ffffff;
            display: flex;
            flex-wrap: wrap;
            align-items: flex-start;

            &.fileName {
              flex-wrap: nowrap;

              .name {
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
              }

              // .ext{

              // }
            }
          }

          .tag-content {
            display: flex;
            flex-wrap: wrap;
            align-items: center;

            :deep(.ant-select-selector) {
              background: #091822;
              border: 1px solid #3a464e;
            }

            .labelTag {
              position: relative;
              padding: 5px 7px;
              background: rgba(216, 216, 216, 0.06);
              color: #fff;
              display: inline-block;
              border-radius: 2px;
              margin-right: 10px;
              margin-bottom: 5px;

              &:hover {
                .labelTagIcon {
                  display: block;
                  color: rgb(179 182 189);
                }
              }

              .labelTagIcon {
                display: none;
                position: absolute;
                width: 15px;
                height: 15px;
                right: -5px;
                top: -5px;
              }
            }

            .addTag {
              padding: 4px 10px;
              background: rgba(216, 216, 216, 0.06);
              border-radius: 2px;
              display: flex;
              align-items: center;
              width: 75px;
              justify-content: space-between;
              border: 1px dashed rgba(255, 255, 255, 0.2);
              cursor: pointer;

              .icon {
                margin-right: 6px;
                color: #c2c6c8;
              }

              .addText {
                font-size: 14px;
                font-family: PingFangSC-Regular, PingFang SC;
                font-weight: 400;
                color: #adb1b6;
              }
            }
          }

          .tree-select {
            width: 226px;
            height: 30px;
            :deep(.ant-select-selector) {
              background: #091822;
              border: 1px solid #3a464e;
              .ant-select-selection-item {
                color: #fff;
              }

              .ant-select-selection-placeholder {
                color: #fff;
              }
            }

            :deep(.titleBox) {
              display: flex;

              .title {
              }
              .but {
              }
            }
          }

          .content {
            display: flex;

            &.row {
              align-items: center;
            }

            &.col {
              flex-direction: column;
            }
          }

          .map-wrap {
            // height: 230px;
            // height: 265px;
            // height: 295px;

            .text {
              display: flex;
              align-items: center;

              span {
                margin-left: 10px;
              }

              .place {
                width: 240px;
                height: 30px;
                background: rgba(216, 216, 216, 0.05);
                border-radius: 2px;
                display: flex;
                align-items: center;
                // margin-bottom: 8px;
              }
            }
          }
        }
      }
    }
  }
}
</style>
